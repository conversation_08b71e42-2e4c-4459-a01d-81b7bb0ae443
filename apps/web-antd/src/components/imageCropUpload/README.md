# ImageCropUpload 图片裁剪上传组件

基于 Cropper.js 的现代化图片裁剪上传组件，支持自定义裁剪比例、实时预览和自动上传功能。

## 特性

- 🎨 **现代化设计** - 符合项目主题的 UI 设计
- ✂️ **灵活裁剪** - 支持多种裁剪比例（1:1、4:3、16:9、自由）
- 👀 **实时预览** - 裁剪过程中实时预览效果
- 📤 **自动上传** - 裁剪完成后自动上传到服务器
- 🎯 **自定义触发器** - 支持自定义触发按钮样式
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🛡️ **类型安全** - 完整的 TypeScript 支持

## 安装依赖

组件依赖 `cropperjs` 库：

```bash
pnpm add cropperjs -w
```

## 基础用法

```vue
<template>
  <ImageCropUpload
    :on-success="handleUploadSuccess"
    :max-size="5"
    :accept="['image/jpeg', 'image/png', 'image/webp']"
  >
    <a-button type="primary">
      <CameraOutlined />
      选择并裁剪图片
    </a-button>
  </ImageCropUpload>
</template>

<script setup lang="ts">
import { ImageCropUpload } from '#/components/imageCropUpload';

const handleUploadSuccess = (url: string) => {
  console.log('上传成功:', url);
  // 处理上传成功后的逻辑
};
</script>
```

## 自定义触发器

```vue
<template>
  <ImageCropUpload :on-success="handleUploadSuccess">
    <div class="custom-trigger">
      <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-200">
        <CameraOutlined class="text-2xl text-gray-500" />
      </div>
    </div>
  </ImageCropUpload>
</template>
```

## 头像上传场景

```vue
<template>
  <div class="relative">
    <img
      :src="avatarUrl || '/default-avatar.png'"
      alt="Avatar"
      class="w-24 h-24 rounded-full object-cover"
    />
    <ImageCropUpload :on-success="handleAvatarSuccess">
      <div class="absolute bottom-0 right-0 rounded-full bg-blue-600 p-2 text-white hover:bg-blue-700 cursor-pointer">
        <CameraOutlined class="w-4 h-4" />
      </div>
    </ImageCropUpload>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const avatarUrl = ref('');

const handleAvatarSuccess = (url: string) => {
  avatarUrl.value = url;
  // 可以在这里调用 API 更新用户头像
};
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `onSuccess` | `(url: string) => void` | - | 上传成功回调函数 |
| `maxSize` | `number` | `5` | 最大文件大小（MB） |
| `accept` | `string[]` | `['image/jpeg', 'image/png', 'image/webp']` | 支持的文件类型 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `success` | `(url: string)` | 上传成功时触发 |
| `error` | `(error: Error)` | 上传失败时触发 |

## 裁剪功能

### 支持的裁剪比例

- **1:1** - 正方形，适合头像
- **4:3** - 传统照片比例
- **16:9** - 宽屏比例
- **自由** - 不限制比例

### 裁剪操作

- **拖拽** - 移动裁剪区域
- **缩放** - 鼠标滚轮或手势缩放
- **调整** - 拖拽裁剪框边缘调整大小
- **重置** - 恢复到初始状态

## 技术实现

- **裁剪库**: Cropper.js
- **上传 API**: `/common/file/upload/image`
- **图片格式**: JPEG (质量 90%)
- **输出尺寸**: 300x300px (可配置)
- **预览尺寸**: 100x100px

## 样式定制

组件使用 Tailwind CSS 构建，支持深度定制：

```vue
<style scoped>
/* 自定义裁剪器样式 */
:deep(.cropper-crop-box) {
  border-radius: 50%; /* 圆形裁剪框 */
}

:deep(.cropper-view-box) {
  border-radius: 50%; /* 圆形预览 */
}
</style>
```

## 注意事项

1. **文件大小限制**: 默认最大 5MB，可通过 `maxSize` 属性调整
2. **文件格式**: 支持 JPEG、PNG、WEBP 格式
3. **浏览器兼容性**: 需要支持 Canvas API 的现代浏览器
4. **网络环境**: 上传功能需要网络连接

## 错误处理

组件内置了完善的错误处理机制：

- 文件格式验证
- 文件大小验证
- 网络错误处理
- 用户友好的错误提示

## 性能优化

- 图片压缩输出
- 懒加载 Cropper.js
- 内存清理机制
- 响应式预览更新
