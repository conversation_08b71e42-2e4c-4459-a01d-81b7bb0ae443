<template>
  <div class="image-crop-upload">
    <!-- 触发按钮 -->
    <div @click="openCropModal" class="cursor-pointer">
      <slot>
        <div class="flex items-center justify-center w-24 h-24 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors">
          <CameraOutlined class="text-2xl text-gray-500" />
        </div>
      </slot>
    </div>

    <!-- 裁剪模态框 -->
    <a-modal
      v-model:open="cropModalVisible"
      title="裁剪头像"
      width="800px"
      :footer="null"
      :maskClosable="false"
      @cancel="handleCancel"
    >
      <div class="crop-modal-content">
        <!-- 文件选择区域 -->
        <div v-if="!selectedImage" class="file-select-area">
          <a-upload
            :before-upload="handleFileSelect"
            :show-upload-list="false"
            accept="image/*"
            class="w-full"
          >
            <div class="flex flex-col items-center justify-center p-12 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 transition-colors cursor-pointer">
              <CloudUploadOutlined class="text-4xl text-gray-400 mb-4" />
              <p class="text-lg text-gray-600 mb-2">点击选择图片</p>
              <p class="text-sm text-gray-400">支持 JPG、PNG、WEBP 格式，文件大小不超过 5MB</p>
            </div>
          </a-upload>
        </div>

        <!-- 裁剪区域 -->
        <div v-else class="crop-area">
          <div class="flex gap-6">
            <!-- 左侧：裁剪器 -->
            <div class="flex-1">
              <div class="crop-container">
                <img
                  ref="cropperImageRef"
                  :src="selectedImage"
                  alt="Crop Image"
                  class="max-w-full"
                />
              </div>
            </div>

            <!-- 右侧：预览和控制 -->
            <div class="w-64 flex flex-col">
              <!-- 预览区域 -->
              <div class="preview-section mb-6">
                <h4 class="text-sm font-medium text-gray-700 mb-3">预览</h4>
                <div class="preview-container bg-gray-50 rounded-lg p-4">
                  <div class="preview-item mb-4">
                    <p class="text-xs text-gray-500 mb-2">头像预览 (100x100)</p>
                    <div
                      ref="previewRef"
                      class="w-20 h-20 rounded-full overflow-hidden border-2 border-gray-200 mx-auto"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- 裁剪比例选择 -->
              <div class="aspect-ratio-section mb-6">
                <h4 class="text-sm font-medium text-gray-700 mb-3">裁剪比例</h4>
                <div class="grid grid-cols-2 gap-2">
                  <button
                    v-for="ratio in aspectRatios"
                    :key="ratio.label"
                    @click="setAspectRatio(ratio.value)"
                    :class="[
                      'px-3 py-2 text-xs rounded border transition-colors',
                      currentAspectRatio === ratio.value
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-400'
                    ]"
                  >
                    {{ ratio.label }}
                  </button>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="actions-section">
                <div class="flex flex-col gap-2">
                  <a-button @click="resetCrop" class="w-full">
                    <ReloadOutlined />
                    重置
                  </a-button>
                  <a-button @click="selectNewImage" class="w-full">
                    <PictureOutlined />
                    重新选择
                  </a-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部操作按钮 -->
          <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
            <a-button @click="handleCancel">取消</a-button>
            <a-button
              type="primary"
              :loading="uploading"
              @click="handleCropAndUpload"
            >
              {{ uploading ? '上传中...' : '裁剪并上传' }}
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  CameraOutlined,
  CloudUploadOutlined,
  ReloadOutlined,
  PictureOutlined,
} from '@ant-design/icons-vue';
import Cropper from 'cropperjs';
import 'cropperjs/dist/cropper.css';
import { FileApi } from '#/api/common/file';

defineOptions({
  name: 'ImageCropUpload',
});

interface Props {
  /** 上传成功后的回调 */
  onSuccess?: (url: string) => void;
  /** 最大文件大小（MB） */
  maxSize?: number;
  /** 支持的文件类型 */
  accept?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  maxSize: 5,
  accept: () => ['image/jpeg', 'image/png', 'image/webp'],
});

const emit = defineEmits<{
  success: [url: string];
  error: [error: Error];
}>();

// 响应式数据
const cropModalVisible = ref(false);
const selectedImage = ref('');
const uploading = ref(false);
const cropperImageRef = ref<HTMLImageElement>();
const previewRef = ref<HTMLDivElement>();

// Cropper 实例
let cropperInstance: Cropper | null = null;
const currentAspectRatio = ref(1);

// 裁剪比例选项
const aspectRatios = [
  { label: '1:1', value: 1 },
  { label: '4:3', value: 4/3 },
  { label: '16:9', value: 16/9 },
  { label: '自由', value: NaN },
];

// 打开裁剪模态框
const openCropModal = () => {
  cropModalVisible.value = true;
};

// 处理文件选择
const handleFileSelect = (file: File) => {
  // 验证文件类型
  if (!props.accept.includes(file.type)) {
    message.error('不支持的文件格式，请选择 JPG、PNG 或 WEBP 格式的图片');
    return false;
  }

  // 验证文件大小
  if (file.size / 1024 / 1024 > props.maxSize) {
    message.error(`文件大小不能超过 ${props.maxSize}MB`);
    return false;
  }

  // 读取文件并显示
  const reader = new FileReader();
  reader.onload = (e) => {
    selectedImage.value = e.target?.result as string;
    nextTick(() => {
      initCropper();
    });
  };
  reader.readAsDataURL(file);

  return false; // 阻止默认上传行为
};

// 初始化裁剪器
const initCropper = () => {
  if (!cropperImageRef.value) return;

  // 销毁现有实例
  if (cropperInstance) {
    cropperInstance.destroy();
  }

  // 创建新的裁剪器实例
  cropperInstance = new Cropper(cropperImageRef.value, {
    aspectRatio: currentAspectRatio.value,
    viewMode: 1,
    dragMode: 'move',
    autoCropArea: 0.8,
    restore: false,
    guides: true,
    center: true,
    highlight: false,
    cropBoxMovable: true,
    cropBoxResizable: true,
    toggleDragModeOnDblclick: false,
    preview: previewRef.value,
    ready() {
      // 裁剪器准备就绪
    },
    crop() {
      // 裁剪时更新预览
      updatePreview();
    },
  });
};

// 更新预览
const updatePreview = () => {
  if (!cropperInstance || !previewRef.value) return;

  const canvas = cropperInstance.getCroppedCanvas({
    width: 100,
    height: 100,
  });

  previewRef.value.innerHTML = '';
  previewRef.value.appendChild(canvas);
};

// 设置裁剪比例
const setAspectRatio = (ratio: number) => {
  currentAspectRatio.value = ratio;
  if (cropperInstance) {
    cropperInstance.setAspectRatio(ratio);
  }
};

// 重置裁剪
const resetCrop = () => {
  if (cropperInstance) {
    cropperInstance.reset();
  }
};

// 重新选择图片
const selectNewImage = () => {
  selectedImage.value = '';
  if (cropperInstance) {
    cropperInstance.destroy();
    cropperInstance = null;
  }
};

// 裁剪并上传
const handleCropAndUpload = async () => {
  if (!cropperInstance) {
    message.error('请先选择图片');
    return;
  }

  try {
    uploading.value = true;

    // 获取裁剪后的画布
    const canvas = cropperInstance.getCroppedCanvas({
      width: 300,
      height: 300,
      imageSmoothingEnabled: true,
      imageSmoothingQuality: 'high',
    });

    // 将画布转换为 Blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob!);
      }, 'image/jpeg', 0.9);
    });

    // 创建 File 对象
    const file = new File([blob], `avatar_${Date.now()}.jpg`, {
      type: 'image/jpeg',
    });

    // 上传文件
    const response = await FileApi.uploadImage(file);
    
    if (response.fileUrl) {
      message.success('头像上传成功');
      
      // 触发成功回调
      props.onSuccess?.(response.fileUrl);
      emit('success', response.fileUrl);
      
      // 关闭模态框
      handleCancel();
    } else {
      throw new Error('上传失败，未返回文件URL');
    }
  } catch (error) {
    console.error('Upload error:', error);
    message.error('上传失败，请重试');
    emit('error', error as Error);
  } finally {
    uploading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  cropModalVisible.value = false;
  selectedImage.value = '';
  if (cropperInstance) {
    cropperInstance.destroy();
    cropperInstance = null;
  }
};

// 组件卸载时清理
onUnmounted(() => {
  if (cropperInstance) {
    cropperInstance.destroy();
  }
});
</script>

<style scoped>
.image-crop-upload {
  display: inline-block;
}

.crop-modal-content {
  padding: 0;
}

.file-select-area {
  padding: 20px 0;
}

.crop-area {
  padding: 20px 0;
}

.crop-container {
  max-height: 400px;
  overflow: hidden;
}

.crop-container img {
  max-width: 100%;
  display: block;
}

.preview-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.preview-container :deep(canvas) {
  border-radius: 50%;
}

/* Cropper.js 样式覆盖 */
:deep(.cropper-container) {
  font-size: 0;
  line-height: 0;
  position: relative;
  user-select: none;
  direction: ltr;
  touch-action: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

:deep(.cropper-crop-box) {
  border-radius: 50%;
}

:deep(.cropper-view-box) {
  border-radius: 50%;
  outline: 1px solid #39f;
  outline-color: rgba(51, 153, 255, 0.75);
}
</style>
