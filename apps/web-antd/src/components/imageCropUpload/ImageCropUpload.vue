<template>
  <div class="image-crop-upload">
    <!-- 触发按钮 -->
    <div @click="openCropModal" class="cursor-pointer">
      <slot>
        <div class="flex items-center justify-center w-24 h-24 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors">
          <Camera class="text-2xl text-gray-500" />
        </div>
      </slot>
    </div>

    <!-- 裁剪模态框 -->
    <a-modal
      v-model:open="cropModalVisible"
      title="裁剪头像"
      width="800px"
      :footer="null"
      :maskClosable="false"
      @cancel="handleCancel"
    >
      <div class="crop-modal-content">
        <!-- 文件选择区域 -->
        <div v-if="!selectedImage" class="file-select-area">
          <a-upload
            :before-upload="handleFileSelect"
            :show-upload-list="false"
            accept="image/*"
            class="w-full"
          >
            <div class="flex flex-col items-center justify-center p-12 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 transition-colors cursor-pointer">
              <Upload class="text-4xl text-gray-400 mb-4" />
              <p class="text-lg text-gray-600 mb-2">点击选择图片</p>
              <p class="text-sm text-gray-400">支持 JPG、PNG、WEBP 格式，文件大小不超过 5MB</p>
            </div>
          </a-upload>
        </div>

        <!-- 裁剪区域 -->
        <div v-else class="crop-area">
          <div class="flex gap-6">
            <!-- 左侧：裁剪器 -->
            <div class="flex-1">
              <div class="crop-container">
                <img
                  ref="cropperImageRef"
                  :src="selectedImage"
                  alt="Crop Image"
                  class="max-w-full"
                />
              </div>
            </div>

            <!-- 右侧：预览和控制 -->
            <div class="w-64 flex flex-col">
              <!-- 预览区域 -->
              <div class="preview-section mb-6">
                <h4 class="text-sm font-medium text-gray-700 mb-3">预览</h4>
                <div class="preview-container bg-gray-50 rounded-lg p-4">
                  <div class="preview-item mb-4">
                    <p class="text-xs text-gray-500 mb-2">头像预览 (100x100)</p>
                    <div
                      ref="previewRef"
                      class="w-20 h-20 rounded-full overflow-hidden border-2 border-gray-200 mx-auto"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- 裁剪比例选择 -->
              <div class="aspect-ratio-section mb-6">
                <h4 class="text-sm font-medium text-gray-700 mb-3">裁剪比例</h4>
                <div class="grid grid-cols-2 gap-2">
                  <button
                    v-for="ratio in aspectRatios"
                    :key="ratio.label"
                    @click="setAspectRatio(ratio.value)"
                    :class="[
                      'px-3 py-2 text-xs rounded border transition-colors',
                      currentAspectRatio === ratio.value
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-400'
                    ]"
                  >
                    {{ ratio.label }}
                  </button>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="actions-section">
                <div class="flex flex-col gap-2">
                  <a-button @click="resetCrop" class="w-full">
                    <RotateCw />
                    重置
                  </a-button>
                  <a-button @click="selectNewImage" class="w-full">
                    <Image />
                    重新选择
                  </a-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部操作按钮 -->
          <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
            <a-button @click="handleCancel">取消</a-button>
            <a-button
              type="primary"
              :loading="uploading"
              @click="handleCropAndUpload"
            >
              {{ uploading ? '上传中...' : '裁剪并上传' }}
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import { Camera, Upload, RotateCw, Image } from '@vben/icons';
import Cropper from 'cropperjs';
import { FileApi } from '#/api/common/file';

defineOptions({
  name: 'ImageCropUpload',
});

interface Props {
  /** 上传成功后的回调 */
  onSuccess?: (url: string) => void;
  /** 最大文件大小（MB） */
  maxSize?: number;
  /** 支持的文件类型 */
  accept?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  maxSize: 5,
  accept: () => ['image/jpeg', 'image/png', 'image/webp'],
});

const emit = defineEmits<{
  success: [url: string];
  error: [error: Error];
}>();

// 响应式数据
const cropModalVisible = ref(false);
const selectedImage = ref('');
const uploading = ref(false);
const cropperImageRef = ref<HTMLImageElement>();
const previewRef = ref<HTMLDivElement>();

// Cropper 实例
let cropperInstance: Cropper | null = null;
const currentAspectRatio = ref(1);

// 裁剪比例选项
const aspectRatios = [
  { label: '1:1', value: 1 },
  { label: '4:3', value: 4/3 },
  { label: '16:9', value: 16/9 },
  { label: '自由', value: NaN },
];

// 打开裁剪模态框
const openCropModal = () => {
  cropModalVisible.value = true;
};

// 处理文件选择
const handleFileSelect = (file: File) => {
  // 验证文件类型
  if (!props.accept.includes(file.type)) {
    message.error('不支持的文件格式，请选择 JPG、PNG 或 WEBP 格式的图片');
    return false;
  }

  // 验证文件大小
  if (file.size / 1024 / 1024 > props.maxSize) {
    message.error(`文件大小不能超过 ${props.maxSize}MB`);
    return false;
  }

  // 读取文件并显示
  const reader = new FileReader();
  reader.onload = (e) => {
    selectedImage.value = e.target?.result as string;
    nextTick(() => {
      initCropper();
    });
  };
  reader.readAsDataURL(file);

  return false; // 阻止默认上传行为
};

// 初始化裁剪器
const initCropper = () => {
  if (!cropperImageRef.value) return;

  // 销毁现有实例
  if (cropperInstance) {
    cropperInstance.destroy();
  }

  // 创建新的裁剪器实例
  cropperInstance = new Cropper(cropperImageRef.value, {
    aspectRatio: currentAspectRatio.value,
    viewMode: 1,
    dragMode: 'move',
    autoCropArea: 0.8,
    restore: false,
    guides: true,
    center: true,
    highlight: false,
    cropBoxMovable: true,
    cropBoxResizable: true,
    toggleDragModeOnDblclick: false,
    preview: previewRef.value,
    ready() {
      // 裁剪器准备就绪
    },
    crop() {
      // 裁剪时更新预览
      updatePreview();
    },
  });
};

// 更新预览
const updatePreview = () => {
  if (!cropperInstance || !previewRef.value) return;

  const canvas = cropperInstance.getCroppedCanvas({
    width: 100,
    height: 100,
  });

  previewRef.value.innerHTML = '';
  previewRef.value.appendChild(canvas);
};

// 设置裁剪比例
const setAspectRatio = (ratio: number) => {
  currentAspectRatio.value = ratio;
  if (cropperInstance) {
    cropperInstance.setAspectRatio(ratio);
  }
};

// 重置裁剪
const resetCrop = () => {
  if (cropperInstance) {
    cropperInstance.reset();
  }
};

// 重新选择图片
const selectNewImage = () => {
  selectedImage.value = '';
  if (cropperInstance) {
    cropperInstance.destroy();
    cropperInstance = null;
  }
};

// 裁剪并上传
const handleCropAndUpload = async () => {
  if (!cropperInstance) {
    message.error('请先选择图片');
    return;
  }

  try {
    uploading.value = true;

    // 获取裁剪后的画布
    const canvas = cropperInstance.getCroppedCanvas({
      width: 300,
      height: 300,
      imageSmoothingEnabled: true,
      imageSmoothingQuality: 'high',
    });

    // 将画布转换为 Blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob!);
      }, 'image/jpeg', 0.9);
    });

    // 创建 File 对象
    const file = new File([blob], `avatar_${Date.now()}.jpg`, {
      type: 'image/jpeg',
    });

    // 上传文件
    const response = await FileApi.uploadImage(file);
    
    if (response.fileUrl) {
      message.success('头像上传成功');
      
      // 触发成功回调
      props.onSuccess?.(response.fileUrl);
      emit('success', response.fileUrl);
      
      // 关闭模态框
      handleCancel();
    } else {
      throw new Error('上传失败，未返回文件URL');
    }
  } catch (error) {
    console.error('Upload error:', error);
    message.error('上传失败，请重试');
    emit('error', error as Error);
  } finally {
    uploading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  cropModalVisible.value = false;
  selectedImage.value = '';
  if (cropperInstance) {
    cropperInstance.destroy();
    cropperInstance = null;
  }
};

// 组件卸载时清理
onUnmounted(() => {
  if (cropperInstance) {
    cropperInstance.destroy();
  }
});
</script>

<style scoped>
.image-crop-upload {
  display: inline-block;
}

.crop-modal-content {
  padding: 0;
}

.file-select-area {
  padding: 20px 0;
}

.crop-area {
  padding: 20px 0;
}

.crop-container {
  max-height: 400px;
  overflow: hidden;
}

.crop-container img {
  max-width: 100%;
  display: block;
}

.preview-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.preview-container :deep(canvas) {
  border-radius: 50%;
}

/* Cropper.js 2.x 基础样式 */
:deep(.cropper-container) {
  font-size: 0;
  line-height: 0;
  position: relative;
  user-select: none;
  direction: ltr;
  touch-action: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

:deep(.cropper-canvas) {
  position: absolute;
  left: 0;
  top: 0;
}

:deep(.cropper-drag-box) {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  cursor: move;
  background: transparent;
}

:deep(.cropper-crop-box) {
  position: absolute;
  left: 0;
  top: 0;
}

:deep(.cropper-view-box) {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  outline: 1px solid #39f;
  outline-color: rgba(51, 153, 255, 0.75);
  overflow: hidden;
  border-radius: 50%;
}

:deep(.cropper-dashed) {
  position: absolute;
  display: block;
  opacity: 0.5;
  border: 0 dashed #eee;
}

:deep(.cropper-dashed.dashed-h) {
  top: 33.33333%;
  left: 0;
  width: 100%;
  height: 33.33333%;
  border-top-width: 1px;
  border-bottom-width: 1px;
}

:deep(.cropper-dashed.dashed-v) {
  top: 0;
  left: 33.33333%;
  width: 33.33333%;
  height: 100%;
  border-right-width: 1px;
  border-left-width: 1px;
}

:deep(.cropper-center) {
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  width: 0;
  height: 0;
  opacity: 0.75;
}

:deep(.cropper-center:before),
:deep(.cropper-center:after) {
  position: absolute;
  display: block;
  content: ' ';
  background-color: #eee;
}

:deep(.cropper-center:before) {
  top: 0;
  left: -3px;
  width: 7px;
  height: 1px;
}

:deep(.cropper-center:after) {
  top: -3px;
  left: 0;
  width: 1px;
  height: 7px;
}

:deep(.cropper-face),
:deep(.cropper-line),
:deep(.cropper-point) {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  opacity: 0.1;
}

:deep(.cropper-face) {
  top: 0;
  left: 0;
  background-color: #fff;
}

:deep(.cropper-line) {
  background-color: #39f;
}

:deep(.cropper-line.line-e) {
  top: 0;
  right: -3px;
  width: 5px;
  cursor: e-resize;
}

:deep(.cropper-line.line-n) {
  top: -3px;
  left: 0;
  height: 5px;
  cursor: n-resize;
}

:deep(.cropper-line.line-w) {
  top: 0;
  left: -3px;
  width: 5px;
  cursor: w-resize;
}

:deep(.cropper-line.line-s) {
  bottom: -3px;
  left: 0;
  height: 5px;
  cursor: s-resize;
}

:deep(.cropper-point) {
  width: 5px;
  height: 5px;
  background-color: #39f;
  opacity: 0.75;
}

:deep(.cropper-point.point-e) {
  top: 50%;
  right: -3px;
  margin-top: -3px;
  cursor: e-resize;
}

:deep(.cropper-point.point-n) {
  top: -3px;
  left: 50%;
  margin-left: -3px;
  cursor: n-resize;
}

:deep(.cropper-point.point-w) {
  top: 50%;
  left: -3px;
  margin-top: -3px;
  cursor: w-resize;
}

:deep(.cropper-point.point-s) {
  bottom: -3px;
  left: 50%;
  margin-left: -3px;
  cursor: s-resize;
}

:deep(.cropper-point.point-ne) {
  top: -3px;
  right: -3px;
  cursor: ne-resize;
}

:deep(.cropper-point.point-nw) {
  top: -3px;
  left: -3px;
  cursor: nw-resize;
}

:deep(.cropper-point.point-sw) {
  bottom: -3px;
  left: -3px;
  cursor: sw-resize;
}

:deep(.cropper-point.point-se) {
  right: -3px;
  bottom: -3px;
  width: 5px;
  height: 5px;
  cursor: se-resize;
  opacity: 1;
}

:deep(.cropper-point.point-se:before) {
  position: absolute;
  right: -50%;
  bottom: -50%;
  display: block;
  width: 200%;
  height: 200%;
  content: ' ';
  background-color: #39f;
  opacity: 0;
}

:deep(.cropper-invisible) {
  opacity: 0;
}

:deep(.cropper-bg) {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC');
}

:deep(.cropper-hide) {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
}

:deep(.cropper-hidden) {
  display: none !important;
}

:deep(.cropper-move) {
  cursor: move;
}

:deep(.cropper-crop) {
  cursor: crosshair;
}

:deep(.cropper-disabled .cropper-drag-box),
:deep(.cropper-disabled .cropper-face),
:deep(.cropper-disabled .cropper-line),
:deep(.cropper-disabled .cropper-point) {
  cursor: not-allowed;
}
</style>
