export interface ImageCropUploadProps {
  /** 上传成功后的回调 */
  onSuccess?: (url: string) => void;
  /** 最大文件大小（MB） */
  maxSize?: number;
  /** 支持的文件类型 */
  accept?: string[];
}

export interface AspectRatio {
  label: string;
  value: number;
}

export interface CropperOptions {
  aspectRatio: number;
  viewMode: number;
  dragMode: 'crop' | 'move' | 'none';
  autoCropArea: number;
  restore: boolean;
  guides: boolean;
  center: boolean;
  highlight: boolean;
  cropBoxMovable: boolean;
  cropBoxResizable: boolean;
  toggleDragModeOnDblclick: boolean;
}

export interface CropResult {
  canvas: HTMLCanvasElement;
  blob: Blob;
  file: File;
}
