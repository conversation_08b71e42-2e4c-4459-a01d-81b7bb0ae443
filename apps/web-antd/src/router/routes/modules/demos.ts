import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layers',
      order: 1000,
      title: '演示页面',
    },
    name: 'Demos',
    path: '/demos',
    children: [
      {
        name: 'ImageCropUpload',
        path: '/image-crop-upload',
        component: () => import('#/views/demos/image-crop-upload/index.vue'),
        meta: {
          icon: 'lucide:camera',
          title: '图片裁剪上传',
        },
      },
      {
        name: 'AntdDemo',
        path: '/antd',
        component: () => import('#/views/demos/antd/index.vue'),
        meta: {
          icon: 'lucide:layers',
          title: 'Ant Design Vue',
        },
      },
    ],
  },
];

export default routes;
