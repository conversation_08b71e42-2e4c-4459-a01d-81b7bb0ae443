<template>
  <div class="image-crop-demo-page">
    <div class="demo-header">
      <h1>图片裁剪上传组件演示</h1>
      <p>基于 Cropper.js 的现代化图片裁剪上传组件</p>
    </div>
    
    <div class="demo-content">
      <!-- 基础用法 -->
      <div class="demo-section">
        <h3>基础用法</h3>
        <div class="demo-box">
          <div class="flex items-center gap-6">
            <div class="avatar-demo">
              <img
                :src="avatarUrl || '/default-avatar.png'"
                alt="Avatar"
                class="w-24 h-24 rounded-full object-cover border-2 border-gray-200"
              />
            </div>
            <div>
              <ImageCropUpload
                :on-success="handleAvatarSuccess"
                :max-size="5"
                :accept="['image/jpeg', 'image/png', 'image/webp']"
              >
                <a-button type="primary">
                  <CameraOutlined />
                  选择并裁剪头像
                </a-button>
              </ImageCropUpload>
              <p class="text-sm text-gray-500 mt-2">
                支持 JPG、PNG、WEBP 格式，最大 5MB
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 自定义触发器 -->
      <div class="demo-section">
        <h3>自定义触发器</h3>
        <div class="demo-box">
          <div class="flex items-center gap-6">
            <div class="avatar-demo">
              <img
                :src="customAvatarUrl || '/default-avatar.png'"
                alt="Custom Avatar"
                class="w-32 h-32 rounded-full object-cover border-4 border-blue-200"
              />
            </div>
            <div>
              <ImageCropUpload
                :on-success="handleCustomAvatarSuccess"
                :max-size="10"
              >
                <div class="custom-trigger">
                  <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center cursor-pointer hover:from-blue-500 hover:to-purple-600 transition-all duration-300 shadow-lg">
                    <CameraOutlined class="text-2xl text-white" />
                  </div>
                  <p class="text-sm text-gray-600 mt-2 text-center">点击上传</p>
                </div>
              </ImageCropUpload>
            </div>
          </div>
        </div>
      </div>

      <!-- 卡片样式 -->
      <div class="demo-section">
        <h3>卡片样式</h3>
        <div class="demo-box">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div
              v-for="(item, index) in cardAvatars"
              :key="index"
              class="avatar-card"
            >
              <div class="relative">
                <img
                  :src="item.url || '/default-avatar.png'"
                  :alt="`Avatar ${index + 1}`"
                  class="w-full h-48 object-cover rounded-lg"
                />
                <ImageCropUpload
                  :on-success="(url) => handleCardAvatarSuccess(index, url)"
                  :max-size="5"
                >
                  <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 rounded-lg flex items-center justify-center cursor-pointer">
                    <div class="opacity-0 hover:opacity-100 transition-opacity duration-300">
                      <div class="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                        <CameraOutlined class="text-xl text-gray-700" />
                      </div>
                    </div>
                  </div>
                </ImageCropUpload>
              </div>
              <div class="mt-3">
                <h4 class="font-medium text-gray-900">头像 {{ index + 1 }}</h4>
                <p class="text-sm text-gray-500">点击图片进行裁剪上传</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 上传结果展示 -->
      <div v-if="uploadResults.length > 0" class="demo-section">
        <h3>上传结果</h3>
        <div class="demo-box">
          <div class="upload-results">
            <div
              v-for="(result, index) in uploadResults"
              :key="index"
              class="result-item"
            >
              <div class="flex items-center gap-4">
                <img
                  :src="result.url"
                  alt="Uploaded"
                  class="w-16 h-16 rounded-lg object-cover"
                />
                <div class="flex-1">
                  <p class="font-medium text-gray-900">上传成功</p>
                  <p class="text-sm text-gray-500">{{ result.timestamp }}</p>
                  <a
                    :href="result.url"
                    target="_blank"
                    class="text-sm text-blue-600 hover:text-blue-800"
                  >
                    查看原图
                  </a>
                </div>
                <div class="text-green-500">
                  <CheckCircleOutlined class="text-xl" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { CameraOutlined, CheckCircleOutlined } from '@ant-design/icons-vue';
import { ImageCropUpload } from '#/components/imageCropUpload';

defineOptions({
  name: 'ImageCropUploadDemo',
});

// 响应式数据
const avatarUrl = ref('');
const customAvatarUrl = ref('');
const cardAvatars = ref([
  { url: '' },
  { url: '' },
  { url: '' },
]);

const uploadResults = ref<Array<{ url: string; timestamp: string }>>([]);

// 处理头像上传成功
const handleAvatarSuccess = (url: string) => {
  avatarUrl.value = url;
  addUploadResult(url);
  message.success('头像上传成功！');
};

// 处理自定义头像上传成功
const handleCustomAvatarSuccess = (url: string) => {
  customAvatarUrl.value = url;
  addUploadResult(url);
  message.success('自定义头像上传成功！');
};

// 处理卡片头像上传成功
const handleCardAvatarSuccess = (index: number, url: string) => {
  cardAvatars.value[index].url = url;
  addUploadResult(url);
  message.success(`头像 ${index + 1} 上传成功！`);
};

// 添加上传结果
const addUploadResult = (url: string) => {
  uploadResults.value.unshift({
    url,
    timestamp: new Date().toLocaleString(),
  });
  
  // 只保留最近的 10 个结果
  if (uploadResults.value.length > 10) {
    uploadResults.value = uploadResults.value.slice(0, 10);
  }
};
</script>

<style scoped>
.image-crop-demo-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-header h1 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 28px;
  font-weight: 600;
}

.demo-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 32px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.demo-section h3 {
  margin: 0;
  padding: 16px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  color: #1890ff;
  font-size: 18px;
  font-weight: 600;
}

.demo-box {
  padding: 24px;
}

.avatar-demo {
  flex-shrink: 0;
}

.custom-trigger {
  text-align: center;
}

.avatar-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.avatar-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.upload-results {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}
</style>
